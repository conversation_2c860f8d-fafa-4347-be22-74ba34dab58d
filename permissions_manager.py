# -*- coding: utf-8 -*-
"""
نظام إدارة الصلاحيات المتقدم
Advanced Permissions Management System
"""

import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from enum import Enum
from pathlib import Path

from config import SecurityConfig, AppConfig
from logging_config import smart_logger

class UserRole(Enum):
    """أدوار المستخدمين"""
    ADMIN = "admin"
    MANAGER = "manager"
    ACCOUNTANT = "accountant"
    EMPLOYEE = "employee"
    VIEWER = "viewer"

class Permission(Enum):
    """الصلاحيات المتاحة"""
    # صلاحيات العملاء
    VIEW_CLIENTS = "view_clients"
    ADD_CLIENTS = "add_clients"
    EDIT_CLIENTS = "edit_clients"
    DELETE_CLIENTS = "delete_clients"
    
    # صلاحيات الموردين
    VIEW_SUPPLIERS = "view_suppliers"
    ADD_SUPPLIERS = "add_suppliers"
    EDIT_SUPPLIERS = "edit_suppliers"
    DELETE_SUPPLIERS = "delete_suppliers"
    
    # صلاحيات الموظفين
    VIEW_EMPLOYEES = "view_employees"
    ADD_EMPLOYEES = "add_employees"
    EDIT_EMPLOYEES = "edit_employees"
    DELETE_EMPLOYEES = "delete_employees"
    MANAGE_SALARIES = "manage_salaries"
    
    # صلاحيات المشاريع
    VIEW_PROJECTS = "view_projects"
    ADD_PROJECTS = "add_projects"
    EDIT_PROJECTS = "edit_projects"
    DELETE_PROJECTS = "delete_projects"
    
    # صلاحيات المالية
    VIEW_FINANCES = "view_finances"
    ADD_EXPENSES = "add_expenses"
    ADD_REVENUES = "add_revenues"
    EDIT_FINANCES = "edit_finances"
    DELETE_FINANCES = "delete_finances"
    
    # صلاحيات المخزون
    VIEW_INVENTORY = "view_inventory"
    ADD_INVENTORY = "add_inventory"
    EDIT_INVENTORY = "edit_inventory"
    DELETE_INVENTORY = "delete_inventory"
    
    # صلاحيات الفواتير
    VIEW_INVOICES = "view_invoices"
    CREATE_INVOICES = "create_invoices"
    EDIT_INVOICES = "edit_invoices"
    DELETE_INVOICES = "delete_invoices"
    
    # صلاحيات التقارير
    VIEW_REPORTS = "view_reports"
    EXPORT_REPORTS = "export_reports"
    ADVANCED_REPORTS = "advanced_reports"
    
    # صلاحيات النظام
    MANAGE_USERS = "manage_users"
    MANAGE_PERMISSIONS = "manage_permissions"
    SYSTEM_SETTINGS = "system_settings"
    BACKUP_RESTORE = "backup_restore"
    VIEW_LOGS = "view_logs"

class PermissionsManager:
    """مدير الصلاحيات المتقدم"""
    
    def __init__(self):
        self.role_permissions = self._initialize_role_permissions()
        self.user_sessions = {}
        self.failed_attempts = {}
        self.locked_users = {}
        self.audit_log = []
        self.load_audit_log()
    
    def _initialize_role_permissions(self) -> Dict[UserRole, Set[Permission]]:
        """تهيئة صلاحيات الأدوار"""
        return {
            UserRole.ADMIN: {
                # المدير له جميع الصلاحيات
                *Permission
            },
            
            UserRole.MANAGER: {
                # المدير التنفيذي
                Permission.VIEW_CLIENTS, Permission.ADD_CLIENTS, Permission.EDIT_CLIENTS,
                Permission.VIEW_SUPPLIERS, Permission.ADD_SUPPLIERS, Permission.EDIT_SUPPLIERS,
                Permission.VIEW_EMPLOYEES, Permission.ADD_EMPLOYEES, Permission.EDIT_EMPLOYEES,
                Permission.MANAGE_SALARIES,
                Permission.VIEW_PROJECTS, Permission.ADD_PROJECTS, Permission.EDIT_PROJECTS,
                Permission.VIEW_FINANCES, Permission.ADD_EXPENSES, Permission.ADD_REVENUES,
                Permission.EDIT_FINANCES,
                Permission.VIEW_INVENTORY, Permission.ADD_INVENTORY, Permission.EDIT_INVENTORY,
                Permission.VIEW_INVOICES, Permission.CREATE_INVOICES, Permission.EDIT_INVOICES,
                Permission.VIEW_REPORTS, Permission.EXPORT_REPORTS, Permission.ADVANCED_REPORTS
            },
            
            UserRole.ACCOUNTANT: {
                # المحاسب
                Permission.VIEW_CLIENTS, Permission.EDIT_CLIENTS,
                Permission.VIEW_SUPPLIERS, Permission.EDIT_SUPPLIERS,
                Permission.VIEW_FINANCES, Permission.ADD_EXPENSES, Permission.ADD_REVENUES,
                Permission.EDIT_FINANCES,
                Permission.VIEW_INVOICES, Permission.CREATE_INVOICES, Permission.EDIT_INVOICES,
                Permission.VIEW_REPORTS, Permission.EXPORT_REPORTS,
                Permission.VIEW_INVENTORY
            },
            
            UserRole.EMPLOYEE: {
                # الموظف
                Permission.VIEW_CLIENTS, Permission.ADD_CLIENTS,
                Permission.VIEW_SUPPLIERS, Permission.ADD_SUPPLIERS,
                Permission.VIEW_PROJECTS,
                Permission.VIEW_INVENTORY, Permission.ADD_INVENTORY,
                Permission.VIEW_INVOICES, Permission.CREATE_INVOICES,
                Permission.VIEW_REPORTS
            },
            
            UserRole.VIEWER: {
                # المشاهد فقط
                Permission.VIEW_CLIENTS,
                Permission.VIEW_SUPPLIERS,
                Permission.VIEW_PROJECTS,
                Permission.VIEW_INVENTORY,
                Permission.VIEW_INVOICES,
                Permission.VIEW_REPORTS
            }
        }
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """مصادقة المستخدم"""
        try:
            # التحقق من القفل
            if self.is_user_locked(username):
                self.log_security_event("login_attempt_locked", {
                    "username": username,
                    "reason": "User account is locked"
                })
                return None
            
            # هنا يجب التحقق من قاعدة البيانات
            # للتبسيط، سنستخدم مستخدم افتراضي
            if username == "admin" and password == "admin":
                user_data = {
                    "id": 1,
                    "username": username,
                    "role": UserRole.ADMIN.value,
                    "full_name": "المدير العام"
                }
                
                # إنشاء جلسة
                session_token = self.create_session(user_data)
                user_data["session_token"] = session_token
                
                # تسجيل نجاح تسجيل الدخول
                self.log_security_event("login_success", {
                    "username": username,
                    "role": user_data["role"]
                })
                
                # إعادة تعيين محاولات الفشل
                if username in self.failed_attempts:
                    del self.failed_attempts[username]
                
                return user_data
            else:
                # تسجيل فشل تسجيل الدخول
                self.record_failed_attempt(username)
                self.log_security_event("login_failed", {
                    "username": username,
                    "reason": "Invalid credentials"
                })
                return None
                
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في مصادقة المستخدم")
            return None
    
    def create_session(self, user_data: Dict) -> str:
        """إنشاء جلسة مستخدم"""
        try:
            session_token = hashlib.sha256(
                f"{user_data['username']}{datetime.now().isoformat()}".encode()
            ).hexdigest()
            
            session_info = {
                "user_id": user_data["id"],
                "username": user_data["username"],
                "role": user_data["role"],
                "created_at": datetime.now(),
                "expires_at": datetime.now() + timedelta(seconds=SecurityConfig.SESSION_TIMEOUT),
                "last_activity": datetime.now()
            }
            
            self.user_sessions[session_token] = session_info
            return session_token
            
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في إنشاء الجلسة")
            return ""
    
    def validate_session(self, session_token: str) -> Optional[Dict]:
        """التحقق من صحة الجلسة"""
        try:
            if session_token not in self.user_sessions:
                return None
            
            session = self.user_sessions[session_token]
            
            # التحقق من انتهاء الجلسة
            if datetime.now() > session["expires_at"]:
                del self.user_sessions[session_token]
                return None
            
            # تحديث آخر نشاط
            session["last_activity"] = datetime.now()
            
            return session
            
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في التحقق من الجلسة")
            return None
    
    def has_permission(self, user_role: str, permission: Permission) -> bool:
        """التحقق من وجود صلاحية"""
        try:
            role_enum = UserRole(user_role)
            return permission in self.role_permissions.get(role_enum, set())
        except (ValueError, KeyError):
            return False
    
    def get_user_permissions(self, user_role: str) -> List[str]:
        """الحصول على صلاحيات المستخدم"""
        try:
            role_enum = UserRole(user_role)
            permissions = self.role_permissions.get(role_enum, set())
            return [perm.value for perm in permissions]
        except (ValueError, KeyError):
            return []
    
    def record_failed_attempt(self, username: str):
        """تسجيل محاولة فاشلة"""
        if username not in self.failed_attempts:
            self.failed_attempts[username] = []
        
        self.failed_attempts[username].append(datetime.now())
        
        # إزالة المحاولات القديمة (أكثر من ساعة)
        cutoff_time = datetime.now() - timedelta(hours=1)
        self.failed_attempts[username] = [
            attempt for attempt in self.failed_attempts[username]
            if attempt > cutoff_time
        ]
        
        # قفل المستخدم إذا تجاوز الحد الأقصى
        if len(self.failed_attempts[username]) >= SecurityConfig.MAX_LOGIN_ATTEMPTS:
            self.lock_user(username)
    
    def lock_user(self, username: str):
        """قفل المستخدم"""
        self.locked_users[username] = {
            "locked_at": datetime.now(),
            "unlock_at": datetime.now() + timedelta(seconds=SecurityConfig.LOCKOUT_DURATION)
        }
        
        self.log_security_event("user_locked", {
            "username": username,
            "reason": "Too many failed login attempts"
        })
    
    def is_user_locked(self, username: str) -> bool:
        """التحقق من قفل المستخدم"""
        if username not in self.locked_users:
            return False
        
        lock_info = self.locked_users[username]
        if datetime.now() > lock_info["unlock_at"]:
            # إلغاء القفل
            del self.locked_users[username]
            return False
        
        return True
    
    def logout_user(self, session_token: str):
        """تسجيل خروج المستخدم"""
        if session_token in self.user_sessions:
            session = self.user_sessions[session_token]
            self.log_security_event("logout", {
                "username": session["username"]
            })
            del self.user_sessions[session_token]
    
    def cleanup_expired_sessions(self):
        """تنظيف الجلسات المنتهية الصلاحية"""
        current_time = datetime.now()
        expired_sessions = [
            token for token, session in self.user_sessions.items()
            if current_time > session["expires_at"]
        ]
        
        for token in expired_sessions:
            del self.user_sessions[token]
    
    def log_security_event(self, event_type: str, details: Dict):
        """تسجيل حدث أمني"""
        event = {
            "timestamp": datetime.now().isoformat(),
            "event_type": event_type,
            "details": details
        }
        
        self.audit_log.append(event)
        self.save_audit_log()
        
        # تسجيل في نظام السجلات
        smart_logger.log_security_event(event_type, details)
    
    def load_audit_log(self):
        """تحميل سجل التدقيق"""
        try:
            audit_file = AppConfig.LOGS_DIR / "audit.json"
            if audit_file.exists():
                with open(audit_file, 'r', encoding='utf-8') as f:
                    self.audit_log = json.load(f)
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في تحميل سجل التدقيق")
            self.audit_log = []
    
    def save_audit_log(self):
        """حفظ سجل التدقيق"""
        try:
            AppConfig.LOGS_DIR.mkdir(exist_ok=True)
            audit_file = AppConfig.LOGS_DIR / "audit.json"
            
            # الاحتفاظ بآخر 1000 حدث فقط
            if len(self.audit_log) > 1000:
                self.audit_log = self.audit_log[-1000:]
            
            with open(audit_file, 'w', encoding='utf-8') as f:
                json.dump(self.audit_log, f, ensure_ascii=False, indent=2)
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في حفظ سجل التدقيق")
    
    def get_audit_log(self, limit: int = 100) -> List[Dict]:
        """الحصول على سجل التدقيق"""
        return self.audit_log[-limit:] if limit else self.audit_log
    
    def add_custom_permission(self, role: UserRole, permission: Permission):
        """إضافة صلاحية مخصصة لدور"""
        if role not in self.role_permissions:
            self.role_permissions[role] = set()
        
        self.role_permissions[role].add(permission)
        
        self.log_security_event("permission_added", {
            "role": role.value,
            "permission": permission.value
        })
    
    def remove_permission(self, role: UserRole, permission: Permission):
        """إزالة صلاحية من دور"""
        if role in self.role_permissions:
            self.role_permissions[role].discard(permission)
            
            self.log_security_event("permission_removed", {
                "role": role.value,
                "permission": permission.value
            })

# إنشاء مثيل عام من مدير الصلاحيات
permissions_manager = PermissionsManager()

# دوال مساعدة سريعة
def check_permission(user_role: str, permission: Permission) -> bool:
    """فحص صلاحية سريع"""
    return permissions_manager.has_permission(user_role, permission)

def authenticate(username: str, password: str) -> Optional[Dict]:
    """مصادقة سريعة"""
    return permissions_manager.authenticate_user(username, password)

def validate_session(session_token: str) -> Optional[Dict]:
    """التحقق من الجلسة سريع"""
    return permissions_manager.validate_session(session_token)

if __name__ == "__main__":
    # اختبار نظام الصلاحيات
    print("🔧 اختبار نظام الصلاحيات...")
    
    # اختبار المصادقة
    user = authenticate("admin", "admin")
    if user:
        print(f"✅ تم تسجيل الدخول بنجاح: {user['full_name']}")
        
        # اختبار الصلاحيات
        if check_permission(user['role'], Permission.MANAGE_USERS):
            print("✅ المستخدم لديه صلاحية إدارة المستخدمين")
        
        # اختبار الجلسة
        session = validate_session(user['session_token'])
        if session:
            print("✅ الجلسة صالحة")
    else:
        print("❌ فشل تسجيل الدخول")
    
    print("✅ تم اختبار نظام الصلاحيات بنجاح")
